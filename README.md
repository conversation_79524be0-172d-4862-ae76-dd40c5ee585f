# Apple Developer Release Monitor

自动监控Apple Developer RSS并提取Release Notes的工具。

## 功能特性

- 🔄 自动监控Apple Developer RSS更新
- 📄 自动提取Release Notes内容
- 💾 本地保存Release Notes文件
- 🚫 避免重复处理已处理的releases
- 📝 详细的日志记录
- ⚡ 高效的网络请求处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 运行一次检查
```bash
python apple_release_monitor.py --once
```

### 持续监控（默认30分钟检查一次）
```bash
python apple_release_monitor.py
```

## 输出文件

- `release_notes/` - Release Notes文件保存目录
- `processed_releases.json` - 已处理的releases记录
- `apple_releases.log` - 运行日志

## 文件命名规则

Release Notes文件命名格式：`YYYYMMDD_HHMMSS_产品名称.txt`

例如：`20250825_143022_Xcode_26_beta_6.txt`

## 程序特点

- **最小化修改**：只处理新的releases，避免重复工作
- **高效写法**：使用session复用连接，合理的错误处理
- **可读性强**：清晰的类结构和函数命名，详细的注释

## 监控流程

1. 获取RSS内容
2. 解析新的release项目
3. 访问release页面提取Release Notes链接
4. 下载并提取Release Notes内容
5. 保存到本地文件
6. 记录已处理的releases避免重复

## 注意事项

- 程序会自动处理Apple的重定向链接
- 支持多种Release Notes页面格式
- 网络请求包含适当的超时和重试机制
